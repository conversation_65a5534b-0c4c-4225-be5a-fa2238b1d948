'use client';

import { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { GamerProfile } from '@/types/gamer';
import { 
  MapPin, 
  Clock, 
  Calendar, 
  Edit3, 
  Users, 
  Trophy,
  Gamepad2,
  Shield,
  Sword,
  Heart
} from 'lucide-react';

interface ProfileHeaderProps {
  profile: GamerProfile;
  isOwnProfile?: boolean;
  onProfileUpdate?: (updates: Partial<GamerProfile>) => void;
}

export function ProfileHeader({ profile, isOwnProfile = false, onProfileUpdate }: ProfileHeaderProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    displayName: profile.displayName,
    bio: profile.bio || '',
    location: profile.location || '',
  });

  const handleSave = () => {
    onProfileUpdate?.(editForm);
    setIsEditing(false);
  };

  const getPlayStyleIcon = (playStyle: string) => {
    switch (playStyle) {
      case 'competitive':
        return <Sword className="h-4 w-4" />;
      case 'casual':
        return <Heart className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const getPlayStyleColor = (playStyle: string) => {
    switch (playStyle) {
      case 'competitive':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'casual':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    }
  };

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Avatar and Online Status */}
          <div className="flex flex-col items-center md:items-start">
            <div className="relative">
              <Avatar className="h-24 w-24 md:h-32 md:w-32">
                <AvatarImage src={profile.avatar} alt={profile.displayName} />
                <AvatarFallback className="text-2xl">
                  {profile.displayName.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {profile.isOnline && (
                <div
                  className="absolute -bottom-1 -right-1 h-6 w-6 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"
                  aria-label="User is online"
                  title="Online"
                />
              )}
            </div>
            <div className="mt-2 text-center md:text-left">
              <Badge variant={profile.isOnline ? 'default' : 'secondary'} className="text-xs">
                {profile.isOnline ? 'Online' : 'Offline'}
              </Badge>
            </div>
          </div>

          {/* Profile Information */}
          <div className="flex-1 space-y-4">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold">{profile.displayName}</h1>
                <p className="text-muted-foreground">@{profile.username}</p>
              </div>
              
              {isOwnProfile && (
                <Dialog open={isEditing} onOpenChange={setIsEditing}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" aria-label="Edit profile information">
                      <Edit3 className="h-4 w-4 mr-2" aria-hidden="true" />
                      Edit Profile
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                      <DialogTitle>Edit Profile</DialogTitle>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid gap-2">
                        <Label htmlFor="displayName">Display Name</Label>
                        <Input
                          id="displayName"
                          value={editForm.displayName}
                          onChange={(e) => setEditForm({ ...editForm, displayName: e.target.value })}
                          aria-describedby="displayName-description"
                        />
                        <p id="displayName-description" className="sr-only">
                          Enter your display name as it will appear to other users
                        </p>
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="bio">Bio</Label>
                        <Textarea
                          id="bio"
                          value={editForm.bio}
                          onChange={(e) => setEditForm({ ...editForm, bio: e.target.value })}
                          rows={3}
                          aria-describedby="bio-description"
                          maxLength={500}
                        />
                        <p id="bio-description" className="text-xs text-muted-foreground">
                          Tell other gamers about yourself (max 500 characters)
                        </p>
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="location">Location</Label>
                        <Input
                          id="location"
                          value={editForm.location}
                          onChange={(e) => setEditForm({ ...editForm, location: e.target.value })}
                        />
                      </div>
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setIsEditing(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleSave}>Save Changes</Button>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>

            {/* Bio */}
            {profile.bio && (
              <p className="text-sm text-muted-foreground leading-relaxed">
                {profile.bio}
              </p>
            )}

            {/* Profile Details */}
            <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
              {profile.location && (
                <div className="flex items-center gap-1">
                  <MapPin className="h-4 w-4" />
                  <span>{profile.location}</span>
                </div>
              )}
              {profile.timezone && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>{profile.timezone}</span>
                </div>
              )}
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>Joined {profile.joinDate.toLocaleDateString()}</span>
              </div>
            </div>

            {/* Play Style and Stats */}
            <div className="flex flex-wrap gap-2">
              <Badge className={`${getPlayStyleColor(profile.playStyle)} flex items-center gap-1`}>
                {getPlayStyleIcon(profile.playStyle)}
                {profile.playStyle.charAt(0).toUpperCase() + profile.playStyle.slice(1)} Player
              </Badge>
              
              <Badge variant="outline" className="flex items-center gap-1">
                <Gamepad2 className="h-4 w-4" />
                {profile.stats.gamesOwned} Games
              </Badge>
              
              <Badge variant="outline" className="flex items-center gap-1">
                <Trophy className="h-4 w-4" />
                {profile.stats.achievementsUnlocked} Achievements
              </Badge>
              
              <Badge variant="outline" className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {profile.stats.friendsCount} Friends
              </Badge>
            </div>

            {/* Preferred Roles */}
            {profile.preferredRoles.length > 0 && (
              <div>
                <h3 className="text-sm font-medium mb-2">Preferred Roles</h3>
                <div className="flex flex-wrap gap-1">
                  {profile.preferredRoles.map((role) => (
                    <Badge key={role} variant="secondary" className="text-xs">
                      {role}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
