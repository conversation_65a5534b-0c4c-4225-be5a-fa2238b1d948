'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { GameProfile, Character, GamerStats } from '@/types/gamer';
import { GamingFocus } from './gaming-focus';
import { 
  Clock, 
  Trophy, 
  Server, 
  Star,
  Shield,
  Sword,
  Crown,
  Plus,
  ExternalLink
} from 'lucide-react';

interface GamesCharactersProps {
  games: GameProfile[];
  stats: GamerStats;
  isOwnProfile?: boolean;
}

export function GamesCharacters({ games, stats, isOwnProfile = false }: GamesCharactersProps) {
  const [selectedGame, setSelectedGame] = useState<string>(games[0]?.gameId || '');

  const formatHours = (hours: number) => {
    if (hours < 24) {
      return `${hours}h`;
    }
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}d ${remainingHours}h`;
  };

  const getPlatformColor = (platform: string) => {
    const colors: Record<string, string> = {
      steam: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      battlenet: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      epic: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
      playstation: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      xbox: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      nintendo: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    };
    return colors[platform] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  };

  const getCharacterIcon = (characterClass?: string) => {
    // Simple mapping of classes to icons
    const classIcons: Record<string, React.ReactNode> = {
      'Death Knight': <Sword className="h-4 w-4" />,
      'Paladin': <Shield className="h-4 w-4" />,
      'Dark Knight': <Sword className="h-4 w-4" />,
    };
    return classIcons[characterClass || ''] || <Star className="h-4 w-4" />;
  };

  const CharacterCard = ({ character, game }: { character: Character; game: GameProfile }) => (
    <Card className="relative">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src={character.avatar} alt={character.name} />
              <AvatarFallback>
                {getCharacterIcon(character.class)}
              </AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center gap-2">
                <h4 className="font-semibold">{character.name}</h4>
                {character.isPrimary && (
                  <Crown className="h-4 w-4 text-yellow-500" title="Main Character" />
                )}
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Level {character.level}</span>
                {character.class && (
                  <>
                    <span>•</span>
                    <span>{character.class}</span>
                  </>
                )}
                {character.race && (
                  <>
                    <span>•</span>
                    <span>{character.race}</span>
                  </>
                )}
              </div>
            </div>
          </div>
          
          <div className="text-right">
            <Badge variant="outline" className="text-xs">
              Lv. {character.level}
            </Badge>
            {character.itemLevel && (
              <p className="text-xs text-muted-foreground mt-1">
                iLvl {character.itemLevel}
              </p>
            )}
          </div>
        </div>

        <div className="mt-3 space-y-1">
          {character.server && (
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Server className="h-3 w-3" />
              <span>{character.server}</span>
              {character.faction && (
                <>
                  <span>•</span>
                  <span>{character.faction}</span>
                </>
              )}
            </div>
          )}
          {character.specialization && (
            <Badge variant="secondary" className="text-xs">
              {character.specialization}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const GameCard = ({ game }: { game: GameProfile }) => (
    <Card className="cursor-pointer transition-all duration-200 hover:shadow-md hover:border-primary/20 group"
          onClick={() => setSelectedGame(game.gameId)}>
      <CardContent className="p-5">
        {/* Header with game info and platform */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-4">
            <Avatar className="h-14 w-14 ring-2 ring-background shadow-sm">
              <AvatarImage src={game.gameIcon} alt={game.gameName} />
              <AvatarFallback className="text-lg font-semibold">
                {game.gameName.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-1">
              <h3 className="font-bold text-lg group-hover:text-primary transition-colors">
                {game.gameName}
              </h3>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span className="font-medium">{formatHours(game.hoursPlayed)} played</span>
                {game.level && (
                  <>
                    <span className="text-muted-foreground/60">•</span>
                    <span>Level {game.level}</span>
                  </>
                )}
              </div>
            </div>
          </div>

          <div className="text-right space-y-2">
            <Badge className={getPlatformColor(game.platform)} variant="secondary">
              {game.platform.toUpperCase()}
            </Badge>
            {game.rank && (
              <p className="text-sm font-medium text-primary">{game.rank}</p>
            )}
          </div>
        </div>

        {/* Stats row */}
        <div className="flex items-center justify-between pt-3 border-t border-border/50">
          <div className="flex items-center gap-6 text-sm">
            {game.server && (
              <div className="flex items-center gap-1.5 text-muted-foreground">
                <Server className="h-4 w-4" />
                <span>{game.server}</span>
              </div>
            )}
            <div className="flex items-center gap-1.5 text-muted-foreground">
              <Trophy className="h-4 w-4" />
              <span>{game.achievements.length} achievements</span>
            </div>
          </div>

          <div className="text-sm font-medium text-muted-foreground">
            {game.characters.length} character{game.characters.length !== 1 ? 's' : ''}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const selectedGameData = games.find(game => game.gameId === selectedGame);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Games & Characters</h2>
        {isOwnProfile && (
          <Button variant="outline" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Add Game
          </Button>
        )}
      </div>

      <Tabs value="overview" className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="characters">Characters</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6">
          {/* Gaming Focus */}
          <div className="max-w-sm">
            <GamingFocus stats={stats} />
          </div>

          {/* Games Grid */}
          <div className="grid gap-4 md:grid-cols-2">
            {games.map((game) => (
              <GameCard key={game.gameId} game={game} />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="characters" className="space-y-4">
          {selectedGameData && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">{selectedGameData.gameName} Characters</h3>
                {isOwnProfile && (
                  <Button variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Character
                  </Button>
                )}
              </div>
              
              <div className="grid gap-4 md:grid-cols-2">
                {selectedGameData.characters.map((character) => (
                  <CharacterCard 
                    key={character.id} 
                    character={character} 
                    game={selectedGameData} 
                  />
                ))}
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
