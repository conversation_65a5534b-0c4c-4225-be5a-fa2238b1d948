'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { GamerStats } from '@/types/gamer';
import { 
  Clock, 
  Trophy, 
  Users, 
  Gamepad2, 
  Target,
  TrendingUp,
  Star,
  Timer
} from 'lucide-react';

interface GamingStatsProps {
  stats: GamerStats;
}

export function GamingStats({ stats }: GamingStatsProps) {
  const formatHours = (hours: number) => {
    if (hours < 24) {
      return `${hours}h`;
    }
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}d ${remainingHours}h`;
  };

  const formatSessionLength = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  };

  const getAchievementProgress = () => {
    // Mock calculation for achievement progress
    const totalPossibleAchievements = 100;
    return (stats.achievementsUnlocked / totalPossibleAchievements) * 100;
  };

  const getPlaytimeRank = () => {
    if (stats.totalHoursPlayed > 5000) return { rank: 'Legendary', color: 'text-yellow-600' };
    if (stats.totalHoursPlayed > 2000) return { rank: 'Master', color: 'text-purple-600' };
    if (stats.totalHoursPlayed > 1000) return { rank: 'Expert', color: 'text-blue-600' };
    if (stats.totalHoursPlayed > 500) return { rank: 'Advanced', color: 'text-green-600' };
    return { rank: 'Novice', color: 'text-gray-600' };
  };

  const playtimeRank = getPlaytimeRank();

  return (
    <div className="grid gap-6 md:grid-cols-3">
      {/* Total Playtime */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Playtime</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatHours(stats.totalHoursPlayed)}</div>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="outline" className={playtimeRank.color}>
              <Star className="h-3 w-3 mr-1" />
              {playtimeRank.rank}
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground mt-2">
            Avg session: {formatSessionLength(stats.averageSessionLength)}
          </p>
        </CardContent>
      </Card>

      {/* Social Stats */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Social</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Friends</span>
              <span className="text-xl font-bold">{stats.friendsCount}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Guilds</span>
              <span className="text-xl font-bold">{stats.guildsJoined}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Achievements */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Achievements</CardTitle>
          <Trophy className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.achievementsUnlocked}</div>
          <div className="mt-2">
            <Progress value={getAchievementProgress()} className="h-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {getAchievementProgress().toFixed(0)}% completion rate
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
