'use client';

import { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { ProfileHeader } from '@/components/profile/profile-header';
import { GamingStats } from '@/components/profile/gaming-stats';
import { GamesCharacters } from '@/components/profile/games-characters';
import { Achievements } from '@/components/profile/achievements';
import { ProfileSkeleton } from '@/components/profile/profile-skeleton';
import { ProfileProvider, useProfile, useProfileActions } from '@/contexts/profile-context';
import { GamerProfile } from '@/types/gamer';
import { fetchGamerProfile, fetchRecentAchievements, updateGamerProfile } from '@/lib/mock-data';
import {
  Loader2,
  AlertCircle,
  UserPlus,
  MessageCircle,
  Share,
  MoreHorizontal,
  Users,
  Calendar,
  RefreshCw
} from 'lucide-react';

function ProfilePageContent() {
  const params = useParams();
  const username = params.username as string;
  const { state } = useProfile();
  const actions = useProfileActions();
  const [activeTab, setActiveTab] = useState('overview');

  // Mock check if this is the current user's profile
  const isOwnProfile = username === 'shadowhunter92';

  useEffect(() => {
    const loadProfile = async () => {
      try {
        actions.setLoading(true);
        actions.setError(null);

        const [profileData, achievementsData] = await Promise.all([
          fetchGamerProfile(username),
          fetchRecentAchievements(username)
        ]);

        actions.setProfile(profileData);
        actions.setRecentAchievements(achievementsData);
      } catch (err) {
        actions.setError('Failed to load profile. Please try again.');
        console.error('Error loading profile:', err);
      } finally {
        actions.setLoading(false);
      }
    };

    if (username) {
      loadProfile();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [username]);

  const handleProfileUpdate = async (updates: Partial<GamerProfile>) => {
    if (!state.profile) return;

    try {
      actions.setLoading(true);
      const updatedProfile = await updateGamerProfile(state.profile.id, updates);
      actions.updateProfile(updates);
    } catch (err) {
      actions.setError('Failed to update profile. Please try again.');
      console.error('Error updating profile:', err);
    } finally {
      actions.setLoading(false);
    }
  };

  const handleRetry = () => {
    window.location.reload();
  };

  if (state.loading) {
    return (
      <div className="min-h-screen bg-background">
        <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold">Guildie</h1>
              <Separator orientation="vertical" className="h-6" />
              <span className="text-muted-foreground">Profile</span>
            </div>
          </div>
        </nav>
        <main className="container mx-auto px-4 py-6 max-w-6xl">
          <ProfileSkeleton />
        </main>
      </div>
    );
  }

  if (state.error || !state.profile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <h2 className="text-lg font-semibold mb-2">Profile Not Found</h2>
            <p className="text-muted-foreground mb-4">
              {state.error || "The profile you're looking for doesn't exist or has been removed."}
            </p>
            <div className="flex gap-2 justify-center">
              <Button onClick={handleRetry}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button variant="outline" onClick={() => window.history.back()}>
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation Bar */}
      <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h1 className="text-xl font-bold">Guildie</h1>
              <Separator orientation="vertical" className="h-6" />
              <span className="text-muted-foreground">Profile</span>
            </div>
            
            {!isOwnProfile && (
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Friend
                </Button>
                <Button variant="outline" size="sm">
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Message
                </Button>
                <Button variant="outline" size="sm">
                  <Share className="h-4 w-4 mr-2" />
                  Share
                </Button>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6 max-w-6xl">
        <div className="space-y-6">
          {/* Profile Header */}
          <ProfileHeader
            profile={state.profile}
            isOwnProfile={isOwnProfile}
            onProfileUpdate={handleProfileUpdate}
          />

          {/* Profile Content Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="games">Games</TabsTrigger>
              <TabsTrigger value="achievements">Achievements</TabsTrigger>
              <TabsTrigger value="social">Social</TabsTrigger>
            </TabsList>
            
            <TabsContent value="overview" className="space-y-6 mt-6">
              {/* Gaming Statistics */}
              <div>
                <h2 className="text-xl font-semibold mb-4">Gaming Statistics</h2>
                <GamingStats stats={state.profile.stats} />
              </div>
              
              <Separator />
              
              {/* Recent Activity */}
              <div>
                <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardContent className="p-4">
                      <h3 className="font-medium mb-2">Recent Achievements</h3>
                      <div className="space-y-2">
                        {state.recentAchievements.slice(0, 3).map((achievement) => (
                          <div key={achievement.id} className="flex items-center gap-2 text-sm">
                            <div className="h-2 w-2 bg-primary rounded-full" />
                            <span className="truncate">{achievement.title}</span>
                            <span className="text-muted-foreground text-xs">
                              {achievement.unlockedAt.toLocaleDateString()}
                            </span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardContent className="p-4">
                      <h3 className="font-medium mb-2">Active Guilds</h3>
                      <div className="space-y-2">
                        {state.profile.guilds.filter(g => g.isActive).map((guild) => (
                          <div key={guild.guildId} className="flex items-center gap-2 text-sm">
                            <Users className="h-3 w-3 text-muted-foreground" />
                            <span className="truncate">{guild.guildName}</span>
                            <span className="text-muted-foreground text-xs">
                              {guild.role}
                            </span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="games" className="mt-6">
              <GamesCharacters
                games={state.profile.games}
                isOwnProfile={isOwnProfile}
              />
            </TabsContent>

            <TabsContent value="achievements" className="mt-6">
              <Achievements
                achievements={state.profile.achievements}
                recentAchievements={state.recentAchievements}
              />
            </TabsContent>
            
            <TabsContent value="social" className="mt-6">
              <div className="space-y-6">
                <div>
                  <h2 className="text-xl font-semibold mb-4">Guild Memberships</h2>
                  <div className="grid gap-4 md:grid-cols-2">
                    {state.profile.guilds.map((guild) => (
                      <Card key={guild.guildId}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="font-semibold">{guild.guildName}</h3>
                              {guild.guildTag && (
                                <p className="text-sm text-muted-foreground">[{guild.guildTag}]</p>
                              )}
                            </div>
                            <div className="text-right">
                              <p className="text-sm font-medium capitalize">{guild.role}</p>
                              <p className="text-xs text-muted-foreground">
                                {guild.isActive ? 'Active' : 'Inactive'}
                              </p>
                            </div>
                          </div>
                          <div className="mt-2 flex items-center gap-1 text-xs text-muted-foreground">
                            <Calendar className="h-3 w-3" />
                            <span>Joined {guild.joinedAt.toLocaleDateString()}</span>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h2 className="text-xl font-semibold mb-4">Gaming Network</h2>
                  <div className="grid gap-4 md:grid-cols-3">
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold">{state.profile.stats.friendsCount}</div>
                        <div className="text-sm text-muted-foreground">Friends</div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold">{state.profile.guilds.length}</div>
                        <div className="text-sm text-muted-foreground">Guilds Joined</div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="text-2xl font-bold">
                          {state.profile.guilds.filter(g => g.isActive).length}
                        </div>
                        <div className="text-sm text-muted-foreground">Active Guilds</div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>
    </div>
  );
}

export default function ProfilePage() {
  return (
    <ProfileProvider>
      <ProfilePageContent />
    </ProfileProvider>
  );
}
